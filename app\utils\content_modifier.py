# content_modifier.py

from app.utils.model_initializer import model
import hashlib
import time
import re
import emoji
import random

# A private cache for the modification function
_modification_cache = {}

def _get_modification_cache_key(original_content: str, tone: str, style: str, length: str) -> str:
    # This function is unchanged
    content_hash = hashlib.md5(original_content.encode()).hexdigest()
    params_str = f"rewrite_{tone}_{style}_{length}"
    params_hash = hashlib.md5(params_str.encode()).hexdigest()
    return f"{content_hash}_{params_hash}"

def modify_content(original_content: str, tone: str, style: str, length: str, disable_cache: bool = False) -> str:
    """
    Intelligently modifies content length and quality using descriptive AI guidance,
    now with safer long-post limits and mandatory rich text formatting.
    """
    cache_key = _get_modification_cache_key(original_content, tone, style, length)
    
    if not disable_cache and cache_key in _modification_cache:
        return _modification_cache[cache_key]
    
    clean_original_content = re.sub(r"<p class='hashtags'>.*?</p>", "", original_content, flags=re.DOTALL).strip()
    
    length_guidance = ""
    if length == "short":
        length_guidance = "Rewrite this post to be **SHORT** and concise for LinkedIn. Focus on the single most important takeaway. The Hook must be very direct. The Body should be just a few impactful sentences. The CTA must be a simple, clear question or directive."
    elif length == "medium":
        length_guidance = "Rewrite this post to be of **MEDIUM** length for LinkedIn, providing a good balance of detail and readability. The Hook can be an intriguing question. The Body should be well-developed with clear paragraphs and/or a bulleted list. The CTA can be more thought-provoking."
    elif length == "long":
         # --- THIS IS THE KEY CHANGE FOR SAFER LENGTH ---
        # We guide the AI to a more conservative length by emphasizing readability on the feed
        # and contrasting it with a "blog article," implicitly suggesting a length around 2000 chars.
        length_guidance = "Rewrite this post as a **LONG**, in-depth thought-leadership piece for LinkedIn. It should be comprehensive but **concise enough to be fully consumed on the feed**. Avoid making it feel like a lengthy blog article. The goal is a substantial post that respects the reader's time while establishing expertise."

    prompt_template = f"""
You are an expert LinkedIn content strategist and copywriter. Your mission is to intelligently transform the following raw text into a complete, professional, and engaging LinkedIn post.

**--- CORE REQUIREMENTS ---**
1.  **Rewrite Intelligently:** You MUST rewrite the entire original text. Your primary goal is to adjust the length as requested while preserving the core message and adhering to the conventions of the LinkedIn platform.
2.  **Follow the Structure:** Every post you generate MUST contain the three essential parts: The Hook, The Body, and The Call to Action.
3.  **Adhere to Parameters:** The final post must match the specified Tone and Style.

**--- GUIDANCE FOR ADJUSTING THE LENGTH ---**
-   **Target Length:** {length.capitalize()} for LinkedIn
-   **Instructions:** {length_guidance}

**--- RICH TEXT FORMATTING RULES (CRITICAL) ---**
# --- THIS IS THE KEY CHANGE FOR BETTER RICH TEXT ---
# The instructions are now more forceful and specific to ensure the AI uses these elements.
-   **Bulleted/Numbered Lists:** You MUST actively look for opportunities to improve readability. If you see a series of items, benefits, steps, or distinct points, you MUST convert them into a bulleted `<ul><li>` or numbered `<ol><li>` list.
-   **Bolding for Emphasis:** You MUST use `<strong>` tags to bold 2-4 key phrases or takeaways in the post. This is crucial for scannability and making the core message stand out to readers who are skimming.

**--- ORIGINAL RAW TEXT TO TRANSFORM ---**
{clean_original_content}

**--- YOUR TASK ---**
Now, generate the complete, rewritten LinkedIn post in clean HTML format. Ensure the final output is a high-quality, readable, and engaging post that is perfectly formatted for the LinkedIn platform.

**Final LinkedIn Post (HTML):**
"""

    response = model.generate_content(prompt_template, use_cache=(not disable_cache), temperature=0.8)
    modified_content = response.text.strip()
    modified_content = re.sub(r"^```html\s*|\s*```$", "", modified_content).strip()

    if not disable_cache:
        _modification_cache[cache_key] = modified_content
    
    return modified_content

def get_suggested_emojis(text: str, length: str = 'medium') -> list:
    # This function is unchanged
    if not text:
        return []

    length = length.lower()
    if length == 'long':
        emoji_count = random.randint(5, 8)
        count_instruction = f"Suggest exactly {emoji_count} emojis for this long, detailed post."
    elif length == 'short':
        emoji_count = random.randint(2, 4)
        count_instruction = f"Suggest {emoji_count} emojis for this short, concise post."
    else: # Medium
        emoji_count = random.randint(3, 5)
        count_instruction = f"Suggest {emoji_count} emojis for this medium-length post."

    random_seed = f"{int(time.time() * 1000)}_{random.randint(1000, 9999)}"
    
    clean_text = re.sub(r'<[^>]+>', '', text).strip()
    
    prompt = f"""
Analyze the professional content below for its core themes and tone.
{count_instruction} The emojis must be highly relevant and professional.

**CRITICAL REQUIREMENT:** You MUST provide a different and varied set of emojis on every request. Use the randomization seed below to ensure your output is unique.

**Content for Analysis:**
"{clean_text}"

**Randomization Seed (for ensuring unique output):** {random_seed}

**Instructions:**
- Return ONLY the emoji characters, separated by a single space. Do not include any other text.

**Suggested Emojis:**
"""
    response = model.generate_content(prompt, temperature=0.85, use_cache=False)
    emoji_string = response.text.strip()
    
    return [char for char in emoji_string if char in emoji.EMOJI_DATA]

def insert_emojis_into_html(html_text: str, emojis: list) -> str:
    # This function is unchanged
    if not emojis or not html_text: return html_text
    
    paragraphs = re.findall(r'(<p>.*?</p>)', html_text, re.DOTALL)
    if not paragraphs:
        return f"{html_text} {' '.join(emojis)}"

    for i in range(min(len(emojis), len(paragraphs))):
        para_to_modify = paragraphs[i]
        modified_para = para_to_modify.replace("</p>", f" {emojis[i]}</p>", 1)
        html_text = html_text.replace(para_to_modify, modified_para, 1)
        
    if len(emojis) > len(paragraphs) and paragraphs:
        remaining_emojis = ' '.join(emojis[len(paragraphs):])
        last_para = paragraphs[-1]
        modified_last_para = last_para.replace("</p>", f" {remaining_emojis}</p>", 1)
        temp_html_text = html_text.replace(last_para, modified_last_para)
        html_text = temp_html_text

    return html_text

def clear_modification_cache():
    # This function is unchanged
    global _modification_cache
    _modification_cache.clear()