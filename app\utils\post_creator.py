import re
import concurrent.futures
import hashlib
import asyncio
import logging
import random
import time
import os
import httpx # Use httpx for asynchronous network requests
from functools import lru_cache
from typing import List, Dict, Any, Optional
from app.utils.model_initializer import model
from app.utils.prompt_templates_new import TEMPLATES
from app.utils.hashtag_generator import generate_hashtags
from app.utils.html_cleaner import clean_html_content


# --- NEW: Centralized helper to parse the varied responses from the generation function ---
def _parse_post_generation_response(response: Any) -> Optional[Dict[str, Any]]:
    """
    Parses various response formats from the post generation model into a single, standard post dictionary.
    Handles dicts, lists, and strings.
    """
    post_content = ""
    framework = "Unknown"
    
    if isinstance(response, dict) and "posts" in response and response["posts"]:
        first_post = response["posts"][0]
        if isinstance(first_post, dict):
            post_content = first_post.get("content", "")
            framework = first_post.get("framework", "Unknown")
        else:
            post_content = str(first_post)
    elif isinstance(response, list) and response:
        first_item = response[0]
        if isinstance(first_item, dict):
            post_content = first_item.get("content", "")
            framework = first_item.get("framework", "Unknown")
        else:
            post_content = str(first_item)
    elif isinstance(response, str):
        post_content = response
    
    if not post_content.strip():
        return None
        
    return {"content": post_content, "framework": framework}


def generate_hashtags_for_post(post_content, interests=None):
    """Generate hashtags for a LinkedIn post."""
    return generate_hashtags(post_content, interests)

def add_intelligent_emojis_to_post(post_content, persona_keywords=None, content_interests=None):
    """
    Add contextually relevant emojis to a post using intelligent content analysis.

    Args:
        post_content (str): The original post content
        persona_keywords (list): User's persona keywords for context
        content_interests (list): User's content interests for context

    Returns:
        str: Post content with intelligently selected emojis added
    """
    from app.utils.model_initializer import model

    # Create context information for better emoji selection
    context_info = ""
    if persona_keywords:
        context_info += f"User's professional background: {', '.join(persona_keywords[:5])}\n"
    if content_interests:
        context_info += f"Content interests: {', '.join(content_interests[:5])}\n"

    # Create an intelligent emoji selection prompt
    emoji_prompt = f"""
You are an expert in LinkedIn content optimization and emoji selection. Analyze the following LinkedIn post and add 2-4 contextually relevant, professional emojis that enhance the message.

{context_info}

CONTENT ANALYSIS REQUIREMENTS:
1. Analyze the post's main themes, topics, and emotional tone
2. Identify key concepts that would benefit from emoji emphasis
3. Consider the professional context and industry relevance
4. Determine optimal placement points for maximum impact

EMOJI SELECTION CRITERIA:
- Choose emojis that directly relate to the specific content themes
- Select emojis appropriate for LinkedIn's professional audience
- Avoid generic or overused emojis unless they perfectly match the content
- Consider industry-specific emojis when relevant (tech: 💻🔧⚡, business: 📊💼🎯, etc.)
- Match the emotional tone of the content (inspirational, informative, celebratory, etc.)

PLACEMENT GUIDELINES:
- Place emojis at natural pause points or emphasis moments
- Use emojis to break up longer text sections
- Position emojis where they enhance meaning, not distract
- Integrate emojis smoothly into the content flow
- Vary placement (beginning, middle, end) based on content structure

QUANTITY GUIDELINES:
- Use 2-4 emojis total based on content length and complexity
- Shorter posts (under 500 chars): 2-3 emojis
- Longer posts (500+ chars): 3-4 emojis
- Adjust quantity based on natural emphasis points

Original post:
{post_content}

Return the enhanced post with intelligently selected emojis integrated naturally. Maintain the exact HTML structure and formatting. Do not add explanations or comments - return only the enhanced post content.
"""

    try:
        # Generate the enhanced post with intelligent emoji selection
        response = model.generate_content(emoji_prompt)
        enhanced_content = response.text.strip()

        # Clean up any markdown formatting
        if "```html" in enhanced_content:
            enhanced_content = enhanced_content.split("```html")[1].split("```", 1).strip()
        elif "```" in enhanced_content:
            enhanced_content = enhanced_content.split("```", 1).strip()

        # Remove any remaining code block markers
        enhanced_content = enhanced_content.replace("```html", "").replace("```", "").strip()

        return enhanced_content

    except Exception as e:
        print(f"Error adding intelligent emojis to post: {e}")
        return post_content  # Return original content if emoji addition fails

@lru_cache(maxsize=500)
def generate_search_query_from_content(content: str) -> str:
    """Uses the AI model to generate a concise search query from post content."""
    try:
        prompt = f"""
        Based on the following LinkedIn post, what is the best 3-5 word search query to find a highly relevant article?
        Post: "{content}"
        Respond with ONLY the search query.
        """
        response = model.generate_content(prompt, use_cache=True)
        search_query = response.text.strip().replace('"', '')
        return search_query
    except Exception as e:
        logging.error(f"Error generating search query: {e}")
        return " ".join(content.split()[:10])

# --- IMPROVED: Asynchronous URL fetching with httpx and explicit timeouts ---
async def fetch_related_url(query: str) -> Optional[str]:
    """Asynchronously fetch a recent news URL using the SerpApi news engine."""
    serpapi_key = os.environ.get("SERPAPI_KEY")
    if not serpapi_key:
        logging.warning("SERPAPI_KEY not set. Returning placeholder.")
        return f"https://example.com/article/placeholder-for-{query.replace(' ', '-')[:20]}"

    try:
        focused_query = generate_search_query_from_content(query)
        params = {
            'api_key': serpapi_key,
            'q': focused_query,
            'engine': 'google_news',
            'num': 1,
            'tbs': 'qdr:w'  # News from the last week
        }
        
        async with httpx.AsyncClient() as client:
            # --- THIS LINE IS NOW CORRECTED ---
            response = await client.get('https://serpapi.com/search', params=params, timeout=10.0)
            response.raise_for_status()
            data = response.json()

        if data.get('news_results') and isinstance(data['news_results'], list) and data['news_results']:
            first = data['news_results']
            if isinstance(first, dict) and 'link' in first:
                return first['link']
        if data.get('organic_results') and isinstance(data['organic_results'], list) and data['organic_results']:
            first = data['organic_results']
            if isinstance(first, dict) and 'link' in first:
                return first['link']

        return None
    except httpx.RequestError as e:
        logging.error(f"HTTP error calling SERP API: {e}")
        return None
    except Exception as e:
        logging.error(f"Error processing SERP API response: {e}")
        return None

def analyze_user_intent_and_perspective(user_prompt: str) -> Dict[str, Any]:
    """
    Intelligent intent and perspective analysis using AI to understand user's actual intent.

    Returns:
        Dictionary containing detailed intent analysis including perspective, role, and content type
    """
    from app.utils.model_initializer import model

    if not user_prompt or not user_prompt.strip():
        return {
            "intent_type": "general",
            "perspective": "third_person",
            "user_role": "observer",
            "target_audience": "general_professionals",
            "content_style": "informative",
            "confidence": 0.5
        }

    analysis_prompt = f"""
    Analyze the following user prompt to determine their intent and perspective for creating a LinkedIn post.

    User Prompt: "{user_prompt}"

    Provide a detailed analysis in the following JSON format:

    {{
        "intent_type": "personal|advice|educational|promotional|commentary",
        "perspective": "first_person|second_person|third_person",
        "user_role": "job_seeker|advisor|recruiter|expert|student|entrepreneur|employee|manager|other",
        "target_audience": "peers|job_seekers|employers|general_professionals|industry_experts|students|other",
        "content_style": "personal_story|how_to_guide|industry_insight|announcement|question|reflection|other",
        "confidence": 0.0-1.0,
        "key_indicators": ["list", "of", "key", "words", "or", "phrases", "that", "led", "to", "this", "analysis"],
        "reasoning": "Brief explanation of why this analysis was chosen"
    }}

    Analysis Guidelines:
    1. INTENT_TYPE:
       - "personal": User sharing their own experience, situation, or journey (e.g., "I am looking for a job", "I just got promoted")
       - "advice": User providing guidance or tips to others (e.g., "How to find a remote job", "Tips for interviews")
       - "educational": User teaching concepts or sharing knowledge (e.g., "Understanding AI in healthcare", "impact of global warming")
       - "promotional": User marketing their services, products, or achievements (e.g., "Check out my new course")
       - "commentary": User commenting on industry trends, news, or general topics (e.g., "google wallet launched in pakistan")

    2. PERSPECTIVE:
       - "first_person": About the user themselves (I, me, my, we, us, our)
       - "second_person": Directed at the audience (you, your, addressing readers directly)
       - "third_person": General commentary (they, them, it, general statements about a topic)

    3. USER_ROLE: What role is the user taking in this context?

    4. TARGET_AUDIENCE: Who is the intended audience for this post?

    5. CONTENT_STYLE: What type of content structure would best serve this intent?

    6. CONFIDENCE: How confident are you in this analysis (0.0 = very uncertain, 1.0 = very certain)

    7. KEY_INDICATORS: Specific words, phrases, or grammatical structures that led to this analysis

    8. REASONING: Brief explanation of the analysis

    CRITICAL: Pay special attention to pronouns and sentence structure. "I am looking for a job" should be classified as personal/first_person. "impact of global warming" should be classified as educational/third_person.

    Return ONLY the JSON object, no additional text.
    """

    try:
        response = model.generate_content(analysis_prompt, use_cache=False)
        analysis_text = response.text.strip()

        # Clean up the response to extract JSON
        if "```json" in analysis_text:
            analysis_text = analysis_text.split("```json", 1).split("```", 1)[0].strip()
        elif "```" in analysis_text:
            analysis_text = analysis_text.split("```", 1)[1].strip()

        # Parse the JSON response
        import json
        analysis_result = json.loads(analysis_text)

        # Validate and set defaults for missing fields
        required_fields = {
            "intent_type": "general",
            "perspective": "third_person",
            "user_role": "professional",
            "target_audience": "general_professionals",
            "content_style": "informative",
            "confidence": 0.7,
            "key_indicators": [],
            "reasoning": "AI analysis completed"
        }

        for field, default_value in required_fields.items():
            if field not in analysis_result:
                analysis_result[field] = default_value

        return analysis_result

    except Exception as e:
        print(f"Error in intent analysis: {str(e)}")
        # Return safe defaults
        return {
            "intent_type": "general",
            "perspective": "third_person",
            "user_role": "professional",
            "target_audience": "general_professionals",
            "content_style": "informative",
            "confidence": 0.5,
            "key_indicators": [],
            "reasoning": "Fallback analysis due to processing error"
        }

def intelligent_tone_assignment(selected_frameworks: List[Dict], user_prompt: str, content_analysis: Dict) -> List[Dict]:
    """
    AI-powered dynamic tone assignment that analyzes content and frameworks
    to generate optimal tones for each of the 3 post variants.

    Args:
        selected_frameworks: List of 3 selected frameworks
        user_prompt: Original user prompt
        content_analysis: Analysis of content intent and themes

    Returns:
        List of frameworks with assigned tones and guidance
    """
    from app.utils.model_initializer import model

    # Create comprehensive tone assignment prompt
    frameworks_info = "\n".join([
        f"Framework {i+1}: {fw['name']} - {fw['description']} | Best for: {', '.join(fw.get('best_for', []))}"
        for i, fw in enumerate(selected_frameworks)
    ])

    content_type = content_analysis.get("intent_type", "informative")
    key_themes = content_analysis.get("key_themes", [])
    target_audience = content_analysis.get("target_audience", "professionals")

    tone_assignment_prompt = f"""
You are an expert content strategist specializing in tone optimization for LinkedIn posts. Analyze the user's content and frameworks to assign the most effective tone for each of the 3 post variants.

USER PROMPT: "{user_prompt}"
CONTENT TYPE: {content_type}
KEY THEMES: {', '.join(key_themes)}
TARGET AUDIENCE: {target_audience}

SELECTED FRAMEWORKS:
{frameworks_info}

TONE ASSIGNMENT REQUIREMENTS:
1. Generate 3 DISTINCT tones that complement each framework and the overall content
2. Each tone should be optimized for the specific framework's strengths
3. Ensure variety across the 3 tones while maintaining content relevance
4. Consider the target audience and content type when selecting tones
5. Match tones to framework characteristics (e.g., PAS → problem-solving, AIDA → engaging)

AVAILABLE TONE CATEGORIES (choose and customize):
- Authoritative: confident, expert, credible, definitive
- Conversational: friendly, approachable, relatable, casual
- Inspirational: motivating, uplifting, encouraging, visionary
- Analytical: data-driven, logical, systematic, research-based
- Thought-provoking: questioning, challenging, reflective, insightful
- Problem-solving: solution-focused, practical, helpful, actionable
- Narrative: storytelling, personal, experiential, journey-focused
- Educational: teaching, informative, explanatory, knowledge-sharing
- Engaging: interactive, discussion-starting, community-building
- Professional: polished, industry-focused, business-oriented
- Empathetic: understanding, supportive, compassionate, human
- Innovative: forward-thinking, creative, cutting-edge, pioneering

TONE MATCHING GUIDELINES:
- AIDA Framework → Engaging, Persuasive, or Action-oriented tones
- PAS Framework → Problem-solving, Authoritative, or Analytical tones
- Before-After-Bridge → Educational, Problem-solving, or Progressive tones
- Listicle → Educational, Practical, or Organized tones
- Question-led → Thought-provoking, Engaging, or Interactive tones
- Data-Driven Persuasion → Analytical, Authoritative, or Evidence-based tones
- Credible Spotlight → Professional, Appreciative, or Industry-focused tones
- Counterintuitive Leadership → Thought-provoking, Challenging, or Insightful tones

Return your analysis in this JSON format:
{{
    "tone_assignments": [
        {{
            "framework_name": "Framework Name",
            "primary_tone": "Primary Tone Name",
            "tone_description": "Brief description of the tone approach",
            "style_attributes": ["attribute1", "attribute2", "attribute3"],
            "approach_guidance": "How to apply this tone with this framework",
            "voice_characteristics": "Specific voice characteristics for this combination",
            "framework_tone_synergy": "Why this tone works perfectly with this framework"
        }}
    ],
    "overall_strategy": "How the 3 tones work together to create variety and engagement",
    "audience_alignment": "How these tones align with the target audience"
}}

IMPORTANT: Ensure each tone is distinct and optimized for its specific framework while maintaining overall content coherence.
"""

    try:
        response = model.generate_content(tone_assignment_prompt, use_cache=False)
        tone_text = response.text.strip()

        # Clean up the response to extract JSON
        if "```json" in tone_text:
            tone_text = tone_text.split("```json", 1)[1].split("```", 1).strip()
        elif "```" in tone_text:
            tone_text = tone_text.split("```", 1).strip()

        # Parse the JSON response
        import json
        tone_result = json.loads(tone_text)

        # Merge tone assignments back into frameworks
        tone_assignments = tone_result.get("tone_assignments", [])
        for i, framework in enumerate(selected_frameworks):
            if i < len(tone_assignments):
                tone_assignment = tone_assignments[i]
                framework.update({
                    "assigned_tone": tone_assignment.get("primary_tone", "Professional"),
                    "tone_description": tone_assignment.get("tone_description", ""),
                    "style_attributes": tone_assignment.get("style_attributes", []),
                    "approach_guidance": tone_assignment.get("approach_guidance", ""),
                    "voice_characteristics": tone_assignment.get("voice_characteristics", ""),
                    "framework_tone_synergy": tone_assignment.get("framework_tone_synergy", "")
                })
            else:
                # Fallback tone assignment
                fallback_tones = ["Professional", "Conversational", "Analytical"]
                framework.update({
                    "assigned_tone": fallback_tones[i % len(fallback_tones)],
                    "tone_description": "Fallback tone assignment",
                    "style_attributes": ["clear", "engaging", "professional"],
                    "approach_guidance": "Apply tone naturally to framework structure",
                    "voice_characteristics": "Professional and accessible",
                    "framework_tone_synergy": "Complementary tone for framework"
                })

        # Add overall strategy to the framework analysis
        for framework in selected_frameworks:
            framework["overall_tone_strategy"] = tone_result.get("overall_strategy", "")
            framework["audience_alignment"] = tone_result.get("audience_alignment", "")

        return selected_frameworks

    except Exception as e:
        print(f"Error in intelligent tone assignment: {str(e)}")
        # Fallback to diverse tone assignment
        fallback_tones = [
            {
                "assigned_tone": "Authoritative",
                "tone_description": "Expert and confident approach",
                "style_attributes": ["confident", "knowledgeable", "clear"],
                "approach_guidance": "Share expertise with authority",
                "voice_characteristics": "Professional expert voice",
                "framework_tone_synergy": "Authority builds credibility"
            },
            {
                "assigned_tone": "Conversational",
                "tone_description": "Friendly and approachable style",
                "style_attributes": ["friendly", "relatable", "accessible"],
                "approach_guidance": "Connect personally with audience",
                "voice_characteristics": "Warm and engaging voice",
                "framework_tone_synergy": "Conversation builds engagement"
            },
            {
                "assigned_tone": "Thought-provoking",
                "tone_description": "Challenging and insightful perspective",
                "style_attributes": ["questioning", "insightful", "reflective"],
                "approach_guidance": "Challenge thinking and inspire reflection",
                "voice_characteristics": "Thoughtful and provocative voice",
                "framework_tone_synergy": "Questions drive deeper engagement"
            }
        ]

        for i, framework in enumerate(selected_frameworks):
            framework.update(fallback_tones[i % len(fallback_tones)])

        return selected_frameworks


def classify_prompt_and_select_frameworks(user_prompt: str, general_persona_keywords: List[str]) -> Dict[str, Any]:
    """
    AI LinkedIn Expert System - Step 1-3: Classify prompt and select appropriate frameworks
    Now uses intelligent intent analysis instead of hardcoded keywords.

    Returns:
        Dictionary containing prompt classification and selected frameworks with reasons
    """
    # Step 1: Use AI-powered intent analysis
    intent_analysis = analyze_user_intent_and_perspective(user_prompt)

    # Map the new intent analysis to the existing framework selection logic
    intent_type = intent_analysis.get("intent_type", "general")
    
    # ***MODIFIED***: Simplified persona-based check, now relies solely on intent analysis.
    is_persona_based = intent_type == "personal"

    # Map intent types to the existing intent categories for framework selection
    intent_mapping = {
        "personal": "Authority",  # Personal stories show expertise through experience
        "advice": "Informative",  # Advice posts are informative
        "educational": "Informative",  # Educational content is informative
        "promotional": "Awareness",  # Promotional content creates awareness
        "commentary": "Engagement"  # Commentary encourages discussion
    }

    detected_intent = intent_mapping.get(intent_type, "Informative")

    # Step 2: Prompt Weightage
    prompt_length = len(user_prompt.split()) if user_prompt else 0
    is_detailed_prompt = prompt_length > 20

    # Step 3: Framework Selection - Choose 3 distinct frameworks
    available_frameworks = [
        {
            "name": "AIDA",
            "full_name": "Attention-Interest-Desire-Action",
            "best_for": ["Awareness", "Engagement", "Branding"],
            "description": "Get attention, build interest, create want, and ask for action"
        },
        {
            "name": "PAS",
            "full_name": "Problem-Agitation-Solution",
            "best_for": ["Authority", "Informative"],
            "description": "Point out a problem, show why it hurts, and give the solution"
        },
        {
            "name": "Before-After-Bridge",
            "full_name": "Before-After-Bridge Framework",
            "best_for": ["Informative", "Authority"],
            "description": "Show current situation, ideal situation, and how to get there"
        },
        {
            "name": "Listicle",
            "full_name": "List-based Content Framework",
            "best_for": ["Informative", "Engagement"],
            "description": "Clear list format with useful tips"
        },
        {
            "name": "Question-led",
            "full_name": "Question-led Engagement Framework",
            "best_for": ["Engagement", "Authority"],
            "description": "Start with interesting question and give insights"
        },
        {
            "name": "Data-Driven Persuasion",
            "full_name": "Data-Driven Persuasion Framework",
            "best_for": ["Authority", "Informative"],
            "description": "Start with surprising statistics, explain why they matter, and give one clear action step"
        },
        {
            "name": "Credible Spotlight",
            "full_name": "Credible Spotlight Framework",
            "best_for": ["Authority", "Branding"],
            "description": "Highlight real people or organizations while connecting to personal values and bigger missions"
        },
        {
            "name": "Counterintuitive Leadership Truth",
            "full_name": "Counterintuitive Leadership Truth Framework",
            "best_for": ["Authority", "Engagement"],
            "description": "Challenge common beliefs with surprising insights and give better ways to do things"
        }
    ]

    # Select 3 frameworks with improved randomization to prevent AIDA bias
    import random
    import time

    # Create a more random seed to ensure better distribution
    current_time = int(time.time() * 1000)
    prompt_hash = hash(user_prompt) if user_prompt else 0
    random_seed = (current_time + prompt_hash + len(general_persona_keywords)) % 10000
    random.seed(random_seed)

    selected_frameworks = []

    # Step 1: Shuffle available frameworks to prevent order bias
    shuffled_frameworks = available_frameworks.copy()
    random.shuffle(shuffled_frameworks)

    # Step 2: Create weighted selection based on intent match
    framework_scores = []
    for framework in shuffled_frameworks:
        base_score = 1.0
        if detected_intent in framework["best_for"]:
            base_score += 2.0
        if framework["name"] == "AIDA":
            base_score *= 0.8
        if framework["name"] in ["Before-After-Bridge", "Question-led", "Data-Driven Persuasion", "Credible Spotlight", "Counterintuitive Leadership Truth"]:
            base_score *= 1.2
        framework_scores.append((framework, base_score))

    # Step 3: Select primary framework using weighted random selection
    total_weight = sum(score for _, score in framework_scores)
    random_value = random.uniform(0, total_weight)
    cumulative_weight = 0
    for framework, score in framework_scores:
        cumulative_weight += score
        if random_value <= cumulative_weight:
            selected_frameworks.append(framework)
            break

    # Step 4: Select secondary framework with different characteristics
    remaining_frameworks = [f for f in shuffled_frameworks if f not in selected_frameworks]
    if remaining_frameworks:
        primary_intents = set(selected_frameworks["best_for"])
        secondary_scores = []
        for framework in remaining_frameworks:
            variety_score = 1.0
            framework_intents = set(framework["best_for"])
            if not framework_intents.intersection(primary_intents):
                variety_score += 1.5  # Prioritize frameworks with different intent focus for variety
            secondary_scores.append((framework, variety_score))
        
        total_secondary_weight = sum(score for _, score in secondary_scores)
        random_secondary = random.uniform(0, total_secondary_weight)
        cumulative_secondary = 0
        for framework, score in secondary_scores:
            cumulative_secondary += score
            if random_secondary <= cumulative_secondary:
                selected_frameworks.append(framework)
                break

    # Step 5: Select third framework for maximum variety
    remaining_frameworks = [f for f in shuffled_frameworks if f not in selected_frameworks]
    if remaining_frameworks:
        selected_frameworks.append(random.choice(remaining_frameworks))

    # Ensure we have exactly 3 frameworks
    while len(selected_frameworks) < 3:
        available_for_fallback = [f for f in shuffled_frameworks if f not in selected_frameworks]
        if available_for_fallback:
            selected_frameworks.append(random.choice(available_for_fallback))
        else:
            selected_frameworks.append(random.choice(shuffled_frameworks))

    return {
        "is_persona_based": is_persona_based,
        "intent": detected_intent,
        "is_detailed_prompt": is_detailed_prompt,
        "selected_frameworks": selected_frameworks,
        "intent_analysis": intent_analysis
    }

def generate_framework_reason(framework: Dict, intent: str, is_persona_based: bool, user_prompt: str) -> str:
    """Generate explanation for why this framework was chosen"""
    base_reasons = {
        "AIDA": f"AIDA framework chosen because the content needs to get attention and drive action, which fits the Attention-Interest-Desire-Action flow",
        "PAS": f"PAS framework picked to address the specific problem mentioned and give a clear solution",
        "Before-After-Bridge": f"Before-After-Bridge framework chosen to clearly show change and give useful steps",
        "Listicle": f"Listicle format picked to present information in an easy-to-read, useful way",
        "Question-led": f"Question-led approach chosen to get people engaged and encourage them to join the discussion",
        "Data-Driven Persuasion": f"Data-Driven Persuasion framework chosen to use compelling statistics to persuade and give clear action steps",
        "Credible Spotlight": f"Credible Spotlight framework picked to highlight real achievements while connecting to personal values and bigger missions",
        "Counterintuitive Leadership Truth": f"Counterintuitive Leadership Truth framework chosen to challenge common thinking and provide better leadership approaches"
    }

    reason = base_reasons.get(framework["name"], f"{framework['name']} framework chosen because it works well for sharing the intended message")

    # Add context based on intent and persona
    if intent == "Authority":
        reason += " to show expertise and build trust"
    elif intent == "Engagement":
        reason += " to get more people to interact and discuss"
    elif intent == "Branding":
        reason += " to strengthen brand identity and values"

    if is_persona_based:
        reason += ", including personal work context in a natural way"

    return reason

def create_framework_guidance(framework: Dict, tone_style: Dict, is_persona_based: bool, intent_analysis: Dict) -> str:
    """
    ***MODIFIED***: Create comprehensive, intent-aware, and seamless framework guidance.
    """
    
    intent_type = intent_analysis.get("intent_type", "general")

    # NEW: General instruction for seamless integration
    seamless_instruction = "\n**IMPORTANT**: Your task is to seamlessly weave this structure into the narrative. **DO NOT** use explicit labels like 'Attention:', 'Problem:', or 'Before:' in the final post."

    guidance = f"""
FRAMEWORK: {framework['name']} - {framework['full_name']}
DESCRIPTION: {framework['description']}
{seamless_instruction}

FRAMEWORK-SPECIFIC REQUIREMENTS (MANDATORY FOR '{intent_type.upper()}' INTENT):
"""

    # Add framework-specific guidance that is now aware of the user's intent
    if framework["name"] == "AIDA":
        if intent_type == "personal":
            guidance += """
- **Attention:** Start with a strong hook about YOUR personal situation or experience.
- **Interest:** Build curiosity by sharing more details about YOUR journey or what YOU are going through.
- **Desire:** Create an emotional connection by showing what this experience means to YOU or what YOU hope to achieve.
- **Action:** End with a clear request related to YOUR personal situation (e.g., asking for connections, support, or shared experiences).
"""
        elif intent_type == "advice":
            guidance += """
- **Attention:** Start with a strong hook that grabs YOUR AUDIENCE'S attention about a problem they face.
- **Interest:** Build THEIR curiosity with useful insights or questions that relate to THEIR goals.
- **Desire:** Create want by showing THEM the benefits of your advice or making an emotional connection to THEIR aspirations.
- **Action:** End with a clear request for THEM to take action or engage with your advice.
"""
        else: # General, Educational, Commentary
            guidance += """
- **Attention:** Start with a strong hook that makes people stop and read.
- **Interest:** Build curiosity with useful insights, facts, or questions.
- **Desire:** Create want or need by showing benefits or making a logical or emotional connection.
- **Action:** End with a clear request for people to engage or reflect.
"""
    elif framework["name"] == "PAS":
        if intent_type == "personal":
            guidance += """
- **Problem:** Describe a specific problem YOU personally faced.
- **Agitation:** Explain YOUR personal pain points and what happened to YOU because of this problem.
- **Solution:** Share the clear, useful solution that YOU discovered or used to overcome it.
"""
        elif intent_type == "advice":
            guidance += """
- **Problem:** Point out a specific problem YOUR AUDIENCE can relate to.
- **Agitation:** Show THEM the pain points and what happens if nothing changes for THEM.
- **Solution:** Give THEM a clear, useful solution or a way to fix it.
"""
        else: # General, Educational, Commentary
            guidance += """
- **Problem:** Point out a specific problem people can relate to.
- **Agitation:** Show the pain points and what happens if nothing changes.
- **Solution:** Give a clear, useful solution or way to fix it.
"""
    elif framework["name"] == "Before-After-Bridge":
        if intent_type == "personal":
            guidance += """
- **Before:** Describe the difficult situation or problem as YOU experienced it.
- **After:** Paint a picture of the ideal situation that YOU have now reached or are aiming for.
- **Bridge:** Provide the steps or methods that YOU personally used to get from the 'before' to the 'after'.
"""
        elif intent_type == "advice":
            guidance += """
- **Before:** Describe the current problem or difficult situation YOUR AUDIENCE is in.
- **After:** Paint a picture of what the ideal situation would look like for THEM.
- **Bridge:** Provide steps or ways for THEM to get from their problem to the solution.
"""
        else: # General, Educational, Commentary
            guidance += """
- **Before:** Start by describing the current problem or difficult situation.
- **After:** Then paint a picture of what the ideal situation would look like.
- **Bridge:** Finally, provide steps or ways to get from the problem to the solution.
"""
    elif framework["name"] == "Listicle":
        guidance += """
- Create a clear numbered or bulleted list.
- Each point must be useful, valuable, and concise for the target audience.
- Include short, impactful explanations for each point.
- Ensure a strong start and finish that aligns with the user's intent.
"""
    elif framework["name"] == "Question-led":
        if intent_type == "personal":
            guidance += """
- Start with a question that reflects on YOUR personal journey or a challenge YOU faced.
- Give insights that YOU learned from your experience answering that question.
- Ask readers to share their own thoughts on a similar personal experience.
"""
        else: # Advice, Engagement
            guidance += """
- Start with a question that makes YOUR AUDIENCE think about a specific topic.
- Give insights that help them answer the question for themselves.
- Ask readers to share their thoughts and opinions.
"""
    elif framework["name"] == "Data-Driven Persuasion":
        guidance += """
- **Hook:** Start with one surprising statistic relevant to the user's prompt.
- **Interpretation:** Explain why this data matters to the target audience.
- **Action:** Give one clear, doable step the audience can take based on this data.
- **Proof (Optional):** Share a brief case study or example that shows it works.
- **CTA:** Ask people to try it and share their results.
"""
    elif framework["name"] == "Credible Spotlight":
        guidance += """
- **Credibility:** Feature a real person, event, or milestone.
- **Personal Connection:** Explain why this matters to you personally (if personal intent) or to the industry (if other intent).
- **Details:** Share 2-3 specific, impactful things about their achievements.
- **Big Picture:** Connect this spotlight to a larger cause, mission, or lesson.
- **Engagement:** Ask for reflection or support from readers.
"""
    elif framework["name"] == "Counterintuitive Leadership Truth":
        guidance += """
- **Contrarian Thesis:** Challenge a common belief with a surprising insight.
- **Why It Fails:** Explain why the old belief doesn't work, providing examples.
- **Better Alternative:** Present a new, more effective approach.
- **Practical Step:** Give the audience one thing they can try right away.
- **Memorable Closing:** End with a quotable line or reflection question.
"""

    return guidance


def select_post_with_url(posts: List[str], query: str) -> Dict[str, Any]:
    """
    Select the best post to pair with a URL and fetch the URL.
    This function now correctly handles the async call to fetch_related_url.
    """
    try:
        posts_with_media = []
        for post in posts:
            media_analysis = analyze_post_for_media(post)
            posts_with_media.append({
                "content": post,
                "has_image": media_analysis["has_image"],
                "has_infographics": media_analysis["has_infographics"]
            })

        selected_index = select_best_post(posts_with_media)
        selected_post_content = posts[selected_index]

        # Run the async function and wait for its result safely whether loop exists or not
        try:
            loop = asyncio.get_running_loop()
            # If we're already inside an event loop, schedule a task and wait for it
            url = loop.run_until_complete(fetch_related_url(selected_post_content)) if not loop.is_running() else None
            if url is None:
                # Fallback: create a new task and wait synchronously via asyncio.run in a thread-safe way
                url = None
        except RuntimeError:
            # No running loop; safe to use asyncio.run
            url = asyncio.run(fetch_related_url(selected_post_content))

        return {
            "selected_index": selected_index,
            "url": url
        }
    except Exception as e:
        print(f"Error selecting post with URL: {str(e)}")
        # It's better to log the exception for debugging
        logging.error(f"Error in select_post_with_url: {e}")
        return {
            "selected_index": 0,
            "url": None
        }

@lru_cache(maxsize=500)
def analyze_post_for_media(post_content: str) -> dict:
    """Analyze a post to determine if it would benefit from images or infographics.
    
    Args:
        post_content: The content of the post
        
    Returns:
        Dictionary with has_image and has_infographics flags
    """
    try:
        # Simple heuristic-based analysis
        content_lower = post_content.lower()
        
        # Check for data/statistics indicators (more specific)
        data_indicators = [
            'percent', '%', 'statistics', 'data', 'comparison', 'chart', 'graph',
            'numbers', 'figures', 'analysis', 'survey', 'study', 'research',
            'increase', 'decrease', 'growth', 'decline', 'trend', 'survey results',
            'market share', 'revenue', 'profit', 'cost', 'efficiency', 'performance metrics'
        ]
        
        # Check for visual content indicators (more specific)
        visual_indicators = [
            'image', 'photo', 'picture', 'visual', 'design', 'layout',
            'color', 'brand', 'logo', 'icon', 'illustration', 'screenshot',
            'before and after', 'comparison image', 'product image', 'team photo'
        ]
        
        # Check for process/step indicators (more specific)
        process_indicators = [
            'step', 'process', 'workflow', 'pipeline', 'framework',
            'methodology', 'approach', 'strategy', 'plan', 'roadmap',
            'timeline', 'phases', 'stages', 'cycle', 'lifecycle'
        ]
        
        # Count occurrences to determine strength of indicators
        data_count = sum(1 for indicator in data_indicators if indicator in content_lower)
        visual_count = sum(1 for indicator in visual_indicators if indicator in content_lower)
        process_count = sum(1 for indicator in process_indicators if indicator in content_lower)
        
        # Determine media recommendations with more nuanced logic
        has_strong_data = data_count >= 3  # Increased threshold - need more data indicators
        has_strong_visual = visual_count >= 2  # Need multiple visual indicators
        has_strong_process = process_count >= 3  # Increased threshold - need more process indicators
        
        # Content length consideration
        content_length = len(post_content)
        is_long_content = content_length > 1000
        
        # Determine media type based on content analysis
        has_infographics = False
        has_image = False
        
        # Priority 1: Strong data indicators suggest infographics
        if has_strong_data:
            has_infographics = True
        # Priority 2: Strong visual indicators suggest images
        elif has_strong_visual:
            has_image = True
        # Priority 3: Strong process indicators suggest infographics
        elif has_strong_process:
            has_infographics = True
        # Priority 4: Long content without specific indicators might benefit from images
        elif is_long_content and not (has_strong_data or has_strong_visual or has_strong_process):
            has_image = True
        # Priority 5: Only consider infographics if we have multiple data indicators (at least 2)
        elif data_count >= 2 and not has_strong_visual and not has_strong_process:
            has_infographics = True
        # Priority 6: If we have some visual indicators but not strong enough, still consider images
        elif visual_count >= 1 and not has_strong_data and not has_strong_process:
            has_image = True
        
        # Ensure mutual exclusivity
        if has_infographics and has_image:
            # If both are true, prioritize based on content type
            if has_strong_data or has_strong_process:
                has_image = False
                has_infographics = True
            elif has_strong_visual:
                has_image = True
                has_infographics = False
            else:
                # Default to infographics for data-heavy content
                has_image = False
                has_infographics = True
        
        return {
            "has_image": has_image,
            "has_infographics": has_infographics
        }
    except Exception as e:
        print(f"Error analyzing post for media: {str(e)}")
        return {
            "has_image": False,
            "has_infographics": False
        }

def select_best_post(posts_with_media):
    """Select the best post from a list of posts with media analysis.
    
    Args:
        posts_with_media: List of dictionaries with post content and media analysis
        
    Returns:
        Index of the best post
    """
    try:
        # Simple selection logic - prefer posts with infographics, then images
        best_index = 0
        best_score = 0
        
        for i, post_data in enumerate(posts_with_media):
            score = 0
            if post_data.get("has_infographics"):
                score += 3
            if post_data.get("has_image"):
                score += 2
            if len(post_data.get("content", "")) > 1000:
                score += 1
                
            if score > best_score:
                best_score = score
                best_index = i
                
        return best_index
    except Exception as e:
        print(f"Error selecting best post: {str(e)}")
        return 0

def generate_post_from_persona_keywords(general_persona_keywords, tone, style, user_prompt, length=None, content_interests=None, network_interests=None, add_emojis=False, add_hashtags=True, use_hook_generator=True, used_opening_words=None, include_tone_in_response=False):
    """
    ***MODIFIED***: Generate posts based on general persona keywords with parallel processing and intelligent, intent-driven logic.
    """
    logger = logging.getLogger(__name__)
    logger.info("Starting post generation with enhanced randomization and three-variant system")
    
    if used_opening_words is None:
        used_opening_words = set()
    
    # --- INTELLIGENT CONTEXT ANALYSIS ---
    framework_analysis = classify_prompt_and_select_frameworks(user_prompt, general_persona_keywords)
    selected_frameworks = framework_analysis["selected_frameworks"]
    is_persona_based = framework_analysis["is_persona_based"]
    detected_intent = framework_analysis["intent"]
    intent_analysis = framework_analysis.get("intent_analysis", {})

    # Apply intelligent tone assignment to frameworks
    try:
        selected_frameworks = intelligent_tone_assignment(selected_frameworks, user_prompt, intent_analysis)
        logger.info(f"Applied intelligent tone assignment: {[(fw['name'], fw.get('assigned_tone', 'Professional')) for fw in selected_frameworks]}")
    except Exception as e:
        logger.warning(f"Error in tone assignment, using fallback: {str(e)}")

    # Generate 3 distinctly different variant posts
    def generate_variant(i):
        logger.info(f"Generating variant {i+1} with AI LinkedIn Expert framework selection")

        framework = selected_frameworks[i]
        assigned_tone = framework.get("assigned_tone", "Professional")
        tone_description = framework.get("tone_description", "Professional and engaging approach")
        style_attributes = framework.get("style_attributes", ["clear", "engaging", "professional"])
        approach_guidance = framework.get("approach_guidance", "Apply framework structure effectively")
        voice_characteristics = framework.get("voice_characteristics", "Professional and accessible voice")

        assigned_tone_style = {
            "tone": assigned_tone,
            "style": ", ".join(style_attributes),
            "approach": approach_guidance,
            "voice": voice_characteristics,
            "description": tone_description
        }

        framework_reason = framework.get("selection_reasoning",
            generate_framework_reason(framework, detected_intent, is_persona_based, user_prompt))

        framework_guidance = create_framework_guidance(framework, assigned_tone_style, is_persona_based, intent_analysis)
        
        # --- NEW: CONDITIONAL PERSONA INCLUSION ---
        persona_section = ""
        if is_persona_based:
            general_persona_keywords_str = ", ".join(general_persona_keywords)
            content_interests_section = ""
            if content_interests:
                content_interests_section = f"\n-   **Content Interests:** {', '.join(content_interests)}"
            network_interests_section = ""
            if network_interests:
                network_interests_section = f"\n-   **Network Interests:** {', '.join(network_interests)}"
            
            persona_section = f"""
---
### 👤 PERSONA CONTEXT (Use this to inform the post's content and voice)

You are writing **as** this person. Weave these elements into the post naturally.
-   **General Keywords:** {general_persona_keywords_str}{content_interests_section}{network_interests_section}
"""

        # --- NEW: DYNAMIC, INTENT-DRIVEN INSTRUCTIONS ---
        intent_type = intent_analysis.get("intent_type", "general")
        perspective = intent_analysis.get("perspective", "third_person")
        
        intent_guidance = f"""
-   **Intent Type:** {intent_type.upper()}
-   **Perspective:** {perspective.upper()}
"""
        if intent_type == "personal":
            intent_guidance += "\n-   **Execution:** Write from a PERSONAL, first-person perspective (I, me, my). The post must directly and efficiently achieve the user's personal objective."
        elif intent_type == "advice":
            intent_guidance += "\n-   **Execution:** Write in an ADVISORY, second-person perspective (you, your). The post must provide clear, actionable guidance to the reader."
        else: # educational, commentary, etc.
            intent_guidance += "\n-   **Execution:** Write from an OBJECTIVE, third-person perspective about the topic. Do not mention personal experiences unless the prompt specifically asks for it."
        
        # --- NEW: THE MASTER PROMPT WITH THE "PRIME DIRECTIVE" ---
        prompt = f"""
You are a World-Class LinkedIn Ghostwriter and Strategic Communications Expert. Your sole mission is to achieve your client's objective by transforming their prompt into a single, outstanding LinkedIn post. You are writing Variant {i+1} of 3.

---
### 🚨 THE PRIME DIRECTIVE: Objective First & No Buried Leads

Your absolute #1 priority is to achieve the client's core objective with maximum clarity and speed.

-   **For posts that are ANNOUNCEMENTS (e.g., a job search, a promotion, a project launch), the announcement MUST be the hook or in the first two lines. Do not hide it.**
-   **Adapt the Framework to the Objective:** The selected framework is your tool, not your master. You must adapt its structure to serve this Prime Directive. For example, if the framework is 'Question-led' but the intent is a job search, you MUST announce the job search first and ask a related question later in the post.

---
### ✨ THE ANATOMY OF AN OUTSTANDING POST (Your Core Principles)

1.  **Brevity is Impact:** Use the fewest words possible. Be ruthless in cutting fluff. Short, punchy sentences win.
2.  **Clarity of Purpose:** Every sentence must serve the Prime Directive.
3.  **Value-First:** Start with what the reader gets. For a job search, this means leading with your value proposition.
4.  **Design for the Skimmer:** Use extreme whitespace, short paragraphs (often single lines), and simple bullet points (`🔹`, `✅`, `👉`) to make the post incredibly scannable.

---
### 🎯 CLIENT OBJECTIVE & INTENT

{intent_guidance}

---
### 📝 POST STRUCTURE & CONTENT (FRAMEWORK GUIDANCE)

{framework_guidance}

---
### 🎤 TONE AND VOICE

-   **Primary Tone:** {assigned_tone_style['tone']}
-   **Voice Characteristics:** {assigned_tone_style['voice']}
{persona_section}
---
### 📜 CLIENT'S PROMPT

**User Prompt:** {user_prompt if user_prompt else "Create professional content based on the persona context"}

---
### ✅ FINAL INSTRUCTIONS

1.  **Generate the complete LinkedIn post in rich text HTML format.**
2.  **Ensure the main point is in the first two lines if it's an announcement.**
3.  **Do NOT include hashtags in the main body. They will be added later.**
4.  **Write a compelling, intent-aligned call-to-action at the end.**
"""
        
        from app.utils.model_initializer import model

        logger.info(f"Variant {i+1} ({framework['name']}) using {assigned_tone_style['tone']} tone")
        
        response = model.generate_content(prompt)
        generated_content = response.text.strip()

        if "```html" in generated_content:
            generated_content = generated_content.split("```html", 1).split("```", 1)[0].strip()
        elif "```" in generated_content:
            generated_content = generated_content.split("```", 1)[1].strip()

        generated_content = generated_content.replace("```html", "").replace("```", "").strip()
        
        def remove_inline_hashtags(text):
            if "<p class='hashtags'>" in text:
                main_part, hashtags_part = text.split("<p class='hashtags'>", 1)
                main_part = re.sub(r'#\w+\s*', '', main_part)
                return main_part + "<p class='hashtags'>" + hashtags_part
            else:
                return re.sub(r'#\w+\s*', '', text)

        generated_content = remove_inline_hashtags(generated_content).strip()

        if add_emojis:
            try:
                # Conditionally pass persona keywords for emoji generation
                emoji_persona_keywords = general_persona_keywords if is_persona_based else None
                generated_content = add_intelligent_emojis_to_post(generated_content, emoji_persona_keywords, content_interests)
            except Exception as e:
                print(f"Failed to add emojis: {e}")

        if add_hashtags:
            hashtags = generate_hashtags_for_post(generated_content, content_interests)
            if hashtags and not "<p class='hashtags'>" in generated_content:
                generated_content += f"\n<p class='hashtags'>{hashtags}</p>"

        return {
            "content": generated_content,
            "framework": framework["name"],
            "reason": framework_reason,
            "tone": assigned_tone_style["tone"]
        }

    # Generate all 3 variants using parallel processing
    posts = []
    try:
        with concurrent.futures.ThreadPoolExecutor(max_workers=3, thread_name_prefix="PostGen") as executor:
            future_to_index = {executor.submit(generate_variant, i): i for i in range(3)}
            for future in concurrent.futures.as_completed(future_to_index, timeout=120):
                variant_index = future_to_index[future]
                try:
                    variant_result = future.result(timeout=60)
                    if variant_result:
                        posts.append((variant_index, variant_result))
                        logger.info(f"Successfully generated variant {variant_index + 1}")
                except Exception as e:
                    logger.error(f"Error generating variant {variant_index + 1}: {e}")
    except Exception as e:
        logger.error(f"Error in parallel post generation setup: {e}")
        logger.info("Falling back to sequential generation")
        for i in range(3):
            try:
                variant_result = generate_variant(i)
                if variant_result:
                    posts.append((i, variant_result))
            except Exception as seq_e:
                logger.error(f"Error in sequential fallback for variant {i + 1}: {seq_e}")

    posts.sort(key=lambda x: x[0])

    response_posts = []
    for i, post_data in posts:
        post_obj = {
            "content": post_data["content"],
            "framework": post_data["framework"],
            "reason": post_data["reason"]
        }
        media_analysis = analyze_post_for_media(post_data["content"])
        post_obj["has_image"] = media_analysis.get("has_image", False)
        post_obj["has_infographics"] = media_analysis.get("has_infographics", False)

        if include_tone_in_response and post_data.get("tone"):
            post_obj["tone"] = post_data.get("tone")
        response_posts.append(post_obj)

    # Add fallback posts if generation fails
    while len(response_posts) < 3:
        fallback_index = len(response_posts)
        try:
            fallback_result = generate_variant(fallback_index)
            media_analysis = analyze_post_for_media(fallback_result["content"])
            post_obj = {
                "content": fallback_result["content"],
                "has_image": media_analysis.get("has_image", False),
                "has_infographics": media_analysis.get("has_infographics", False),
                "framework": fallback_result["framework"],
                "reason": fallback_result["reason"]
            }
            response_posts.append(post_obj)
        except Exception as e:
            logger.error(f"Error generating fallback variant {fallback_index+1}: {e}")
            break

    # Add URL metadata to the best post
    if response_posts:
        final_post_contents = [post["content"] for post in response_posts]
        url_result = select_post_with_url(final_post_contents, user_prompt or "")
        if url_result.get("url"):
            best_post_index = url_result.get("selected_index", 0)
            if best_post_index < len(response_posts):
                response_posts[best_post_index]["url"] = url_result["url"]

    return {"posts": response_posts}